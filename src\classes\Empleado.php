<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;
use App\classes\Servicio;

class Empleado
{
	// --- Atributos ---
	private ?int    $id            = null;
	private ?string $nombre        = null;
	private ?string $email         = null;
	private ?string $telefono      = null;
	private ?string $direccion     = null;
	private ?string $fecha_ingreso = null;
	private ?float  $porc_comision = null;
	private ?int    $estado        = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Empleado.
	 */
	public function __construct()
	{
		$this->id            = 0; // O null si prefieres no usar 0 por defecto
		$this->nombre        = null;
		$this->email         = null;
		$this->telefono      = null;
		$this->direccion     = null;
		$this->fecha_ingreso = null;
		$this->porc_comision = 0.0; // Default commission percentage
		$this->estado        = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Empleado desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del empleado.
	 *
	 * @return self Instancia de Empleado.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                = new self();
			$objeto->id            = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->nombre        = $resultado['nombre'] ?? null;
			$objeto->email         = $resultado['email'] ?? null;
			$objeto->telefono      = $resultado['telefono'] ?? null;
			$objeto->direccion     = $resultado['direccion'] ?? null;
			$objeto->fecha_ingreso = $resultado['fecha_ingreso'] ?? null;
			$objeto->porc_comision = isset($resultado['porc_comision']) ? (float)$resultado['porc_comision'] : 0.0;
			$objeto->estado        = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Empleado: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un empleado por su ID.
	 *
	 * @param int $id       ID del empleado.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Empleado o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener empleado por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM empleados
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Empleado (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de empleados activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Empleado.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de empleados activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM empleados
            WHERE
            	estado = 1
            ORDER BY
            	nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Empleados: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo empleado en la base de datos a partir de un objeto Empleado.
	 * El objeto Empleado debe estar completamente poblado con los datos requeridos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo empleado creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getNombre())) {
			throw new Exception("El nombre es requerido para crear un empleado.");
		}

		// Validar formato de email si está presente
		if (!empty($this->getEmail()) && !filter_var($this->getEmail(), FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del email no es válido.");
		}

		try {
			return $this->_insert($conexion);
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear empleado: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar un nuevo empleado en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo empleado creado o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO empleados (
            	 nombre
            	,email
            	,telefono
            	,direccion
            	,fecha_ingreso
            	,porc_comision
            	,estado
            ) VALUES (
            	 :nombre
            	,:email
            	,:telefono
            	,:direccion
            	,:fecha_ingreso
            	,:porc_comision
            	,:estado
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
			$statement->bindValue(':email', $this->getEmail(), $this->getEmail() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':telefono', $this->getTelefono(), $this->getTelefono() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':direccion', $this->getDireccion(), $this->getDireccion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':fecha_ingreso', $this->getFecha_ingreso(), $this->getFecha_ingreso() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':porc_comision', $this->getPorc_comision(), PDO::PARAM_STR); // PDO::PARAM_STR for float
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del empleado recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al crear empleado: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un empleado existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos están vacíos o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID sea válido
		if ($this->getId() <= 0) {
			throw new Exception("ID de empleado no válido para modificar.");
		}

		// Validar que el nombre no esté vacío
		if (empty(trim($this->getNombre()))) {
			throw new Exception("El nombre no puede estar vacío.");
		}

		// Validar formato de email si está presente
		if (!empty($this->getEmail()) && !filter_var($this->getEmail(), FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del email no es válido.");
		}

		try {
			return $this->_update($conexion);
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al modificar empleado: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar un empleado existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el empleado
			$query = <<<SQL
            UPDATE empleados SET
                nombre = :nombre,
                email = :email,
                telefono = :telefono,
                direccion = :direccion,
                fecha_ingreso = :fecha_ingreso,
                porc_comision = :porc_comision,
                estado = :estado
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':nombre', trim($this->getNombre()), PDO::PARAM_STR);
			$statement->bindValue(':email', $this->getEmail(), $this->getEmail() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':telefono', $this->getTelefono(), $this->getTelefono() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':direccion', $this->getDireccion(), $this->getDireccion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':fecha_ingreso', $this->getFecha_ingreso(), $this->getFecha_ingreso() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':porc_comision', $this->getPorc_comision(), PDO::PARAM_STR); // PDO::PARAM_STR for float
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar empleado (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un empleado estableciendo su estado a 0.
	 *
	 * @param int $id       ID del empleado a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE empleados SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al desactivar empleado (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Asocia un servicio a este empleado.
	 * Crea un registro en la tabla empleados_servicios.
	 *
	 * @param int $idServicio ID del servicio a asociar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la asociación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos o si el ID del empleado es inválido.
	 */
	public function asociarServicio(int $idServicio, PDO $conexion): bool
	{
		// Validar que el ID del empleado sea válido
		if ($this->getId() <= 0) {
			throw new Exception("ID de empleado inválido para asociar servicio.");
		}

		// Validar que el ID del servicio sea válido
		if ($idServicio <= 0) {
			throw new Exception("ID de servicio inválido.");
		}

		try {
			// Verificar si el servicio existe
			$servicio = Servicio::get($idServicio, $conexion);
			if (!$servicio) {
				throw new Exception("El servicio con ID $idServicio no existe.");
			}

			// Verificar si el servicio está activo
			if (!$servicio->isActivo()) {
				throw new Exception("El servicio con ID $idServicio está inactivo.");
			}

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO empleados_servicios (
            	 id_empleado
            	,id_servicio
            ) VALUES (
            	 :id_empleado
            	,:id_servicio
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros
			$statement->bindValue(':id_empleado', $this->getId(), PDO::PARAM_INT);
			$statement->bindValue(':id_servicio', $idServicio, PDO::PARAM_INT);

			// Ejecutar la consulta
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. duplicado por restricción única)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				// Si ya existe la asociación, no lo consideramos un error
				return true;
			} else {
				throw new Exception("Error de base de datos al asociar servicio (ID: $idServicio) al empleado (ID: {$this->getId()}): " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al asociar servicio al empleado: " . $e->getMessage());
		}
	}

	/**
	 * Desasocia un servicio de este empleado.
	 * Elimina un registro de la tabla empleados_servicios.
	 *
	 * @param int $idServicio ID del servicio a desasociar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desasociación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos o si el ID del empleado es inválido.
	 */
	public function desasociarServicio(int $idServicio, PDO $conexion): bool
	{
		// Validar que el ID del empleado sea válido
		if ($this->getId() <= 0) {
			throw new Exception("ID de empleado inválido para desasociar servicio.");
		}

		// Validar que el ID del servicio sea válido
		if ($idServicio <= 0) {
			throw new Exception("ID de servicio inválido.");
		}

		try {
			// Preparar la consulta DELETE usando Heredoc
			$query = <<<SQL
            DELETE FROM empleados_servicios
            WHERE
            	 id_empleado = :id_empleado
            	AND id_servicio = :id_servicio
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros
			$statement->bindValue(':id_empleado', $this->getId(), PDO::PARAM_INT);
			$statement->bindValue(':id_servicio', $idServicio, PDO::PARAM_INT);

			// Ejecutar la consulta
			$statement->execute();

			// Verificar si se eliminó algún registro
			return $statement->rowCount() > 0;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al desasociar servicio (ID: $idServicio) del empleado (ID: {$this->getId()}): " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al desasociar servicio del empleado: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene todos los servicios asociados a este empleado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Servicio.
	 * @throws Exception Si hay error en DB o si el ID del empleado es inválido.
	 */
	public function getServicios(PDO $conexion): array
	{
		// Validar que el ID del empleado sea válido
		if ($this->getId() <= 0) {
			throw new Exception("ID de empleado inválido para obtener servicios.");
		}

		try {
			// Consulta para obtener los servicios asociados al empleado
			$query = <<<SQL
            SELECT
            	 s.*
            	,es.id as id_empleado_servicio
            FROM servicios s
            INNER JOIN empleados_servicios es ON s.id = es.id_servicio
            WHERE
            	es.id_empleado = :id_empleado
            	AND s.estado = 1
            ORDER BY
            	s.descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_empleado', $this->getId(), PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$servicios = [];
			foreach ($resultados as $resultado) {
				$servicio = Servicio::construct($resultado);
				// Añadir el ID de la relación para facilitar la desasociación si es necesario
				$servicio->setId_empleado_servicio($resultado['id_empleado_servicio'] ?? 0);
				$servicios[] = $servicio;
			}

			return $servicios;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener servicios del empleado (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Verifica si este empleado tiene asociado un servicio específico.
	 *
	 * @param int $idServicio ID del servicio a verificar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si el servicio está asociado, False en caso contrario.
	 * @throws Exception Si hay error en DB o si el ID del empleado es inválido.
	 */
	public function tieneServicio(int $idServicio, PDO $conexion): bool
	{
		// Validar que el ID del empleado sea válido
		if ($this->getId() <= 0) {
			throw new Exception("ID de empleado inválido para verificar servicio.");
		}

		// Validar que el ID del servicio sea válido
		if ($idServicio <= 0) {
			throw new Exception("ID de servicio inválido.");
		}

		try {
			// Consulta para verificar si existe la asociación
			$query = <<<SQL
            SELECT COUNT(*) as total
            FROM empleados_servicios
            WHERE
            	 id_empleado = :id_empleado
            	AND id_servicio = :id_servicio
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_empleado', $this->getId(), PDO::PARAM_INT);
			$statement->bindValue(':id_servicio', $idServicio, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return ($resultado['total'] ?? 0) > 0;

		} catch (PDOException $e) {
			throw new Exception("Error al verificar si el empleado (ID: {$this->getId()}) tiene el servicio (ID: $idServicio): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getNombre(): ?string
	{
		return $this->nombre;
	}

	public function setNombre(?string $nombre): self
	{
		$this->nombre = $nombre;
		return $this;
	}

	public function getEmail(): ?string
	{
		return $this->email;
	}

	public function setEmail(?string $email): self
	{
		$this->email = $email;
		return $this;
	}

	public function getTelefono(): ?string
	{
		return $this->telefono;
	}

	public function setTelefono(?string $telefono): self
	{
		$this->telefono = $telefono;
		return $this;
	}

	public function getDireccion(): ?string
	{
		return $this->direccion;
	}

	public function setDireccion(?string $direccion): self
	{
		$this->direccion = $direccion;
		return $this;
	}

	public function getFecha_ingreso(): ?string
	{
		return $this->fecha_ingreso;
	}

	public function setFecha_ingreso(?string $fecha_ingreso): self
	{
		$this->fecha_ingreso = $fecha_ingreso;
		return $this;
	}

	public function getPorc_comision(): ?float
	{
		return $this->porc_comision;
	}

	public function setPorc_comision(?float $porc_comision): self
	{
		$this->porc_comision = $porc_comision;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el empleado está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

	/**
	 * Valida si este empleado puede realizar todos los servicios especificados.
	 * Verifica que el empleado tenga asociados todos los servicios requeridos.
	 *
	 * @param array $serviciosIds Array de IDs de servicios a validar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array con 'valido' (bool) y 'servicios_faltantes' (array de IDs).
	 * @throws Exception Si hay error en DB o si el ID del empleado es inválido.
	 */
	public function validarServiciosAsociados(array $serviciosIds, PDO $conexion): array
	{
		// Validar que el ID del empleado sea válido
		if ($this->getId() <= 0) {
			throw new Exception("ID de empleado inválido para validar servicios.");
		}

		// Si no hay servicios a validar, retornar válido
		if (empty($serviciosIds)) {
			return [
				'valido' => true,
				'servicios_faltantes' => []
			];
		}

		try {
			// Obtener los servicios asociados al empleado
			$serviciosEmpleado = $this->getServicios($conexion);
			$serviciosEmpleadoIds = array_map(function($servicio) {
				return $servicio->getId();
			}, $serviciosEmpleado);

			// Encontrar servicios faltantes
			$serviciosFaltantes = array_diff($serviciosIds, $serviciosEmpleadoIds);

			return [
				'valido' => empty($serviciosFaltantes),
				'servicios_faltantes' => array_values($serviciosFaltantes)
			];

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al validar servicios del empleado (ID: {$this->getId()}): " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al validar servicios del empleado: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene los nombres de los servicios especificados por sus IDs.
	 * Método auxiliar para obtener descripciones de servicios para mensajes de error.
	 *
	 * @param array $serviciosIds Array de IDs de servicios.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de descripciones de servicios.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerNombresServicios(array $serviciosIds, PDO $conexion): array
	{
		if (empty($serviciosIds)) {
			return [];
		}

		try {
			// Crear placeholders para la consulta IN
			$placeholders = str_repeat('?,', count($serviciosIds) - 1) . '?';

			$query = <<<SQL
			SELECT id, descripcion
			FROM servicios
			WHERE id IN ($placeholders)
			AND estado = 1
			ORDER BY descripcion
			SQL;

			$statement = $conexion->prepare($query);
			$statement->execute($serviciosIds);
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$nombres = [];
			foreach ($resultados as $resultado) {
				$nombres[$resultado['id']] = $resultado['descripcion'];
			}

			return $nombres;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al obtener nombres de servicios: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene los centros de costo asociados a este empleado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos CentroCosto asociados al empleado.
	 * @throws Exception Si hay error en DB.
	 */
	public function obtenerCentrosCostos(PDO $conexion): array
	{
		try {
			// Consulta para obtener centros de costo asociados a este empleado
			$query = <<<SQL
            SELECT
            	cc.*
            FROM centros_costos cc
            INNER JOIN centros_costos_empleados cce ON cc.id = cce.id_centro_costo
            WHERE
            	cce.id_empleado = :id_empleado
            	AND cc.estado = 1
            ORDER BY
            	cc.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_empleado", $this->getId(), PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = \App\classes\CentroCosto::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener centros de costo del empleado (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Sincroniza las asociaciones de centros de costo para este empleado.
	 * Elimina las asociaciones existentes e inserta las nuevas.
	 *
	 * @param array $ids_centros_costos Array de IDs de centros de costo a asociar.
	 * @param PDO   $conexion           Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa.
	 * @throws Exception Si hay error en DB o validación.
	 */
	public function sincronizarCentrosCostos(array $ids_centros_costos, PDO $conexion): bool
	{
		try {
			// Validar que el empleado tenga un ID válido
			if (!$this->getId() || $this->getId() <= 0) {
				throw new Exception("ID de empleado inválido para sincronizar centros de costo.");
			}

			// Iniciar transacción
			$conexion->beginTransaction();

			// 1. Eliminar todas las asociaciones existentes del empleado
			$deleteQuery = <<<SQL
            DELETE FROM centros_costos_empleados
            WHERE id_empleado = :id_empleado
            SQL;

			$deleteStatement = $conexion->prepare($deleteQuery);
			$deleteStatement->bindValue(':id_empleado', $this->getId(), PDO::PARAM_INT);
			$deleteStatement->execute();

			// 2. Insertar las nuevas asociaciones
			if (!empty($ids_centros_costos)) {
				$insertQuery = <<<SQL
                INSERT INTO centros_costos_empleados (id_centro_costo, id_empleado)
                VALUES (:id_centro_costo, :id_empleado)
                SQL;

				$insertStatement = $conexion->prepare($insertQuery);

				foreach ($ids_centros_costos as $id_centro_costo) {
					// Validar que el ID sea un entero positivo
					$id_centro_costo = (int)$id_centro_costo;
					if ($id_centro_costo <= 0) {
						throw new Exception("ID de centro de costo inválido: $id_centro_costo");
					}

					// Validar que el centro de costo existe y está activo
					$centroCosto = \App\classes\CentroCosto::get($id_centro_costo, $conexion);
					if (!$centroCosto || !$centroCosto->isActivo()) {
						throw new Exception("Centro de costo no encontrado o inactivo (ID: $id_centro_costo)");
					}

					$insertStatement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
					$insertStatement->bindValue(':id_empleado', $this->getId(), PDO::PARAM_INT);
					$insertStatement->execute();
				}
			}

			// Confirmar transacción
			$conexion->commit();
			return true;

		} catch (PDOException $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error de base de datos al sincronizar centros de costo del empleado: " . $e->getMessage());
		} catch (Exception $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error al sincronizar centros de costo del empleado: " . $e->getMessage());
		}
	}
}
