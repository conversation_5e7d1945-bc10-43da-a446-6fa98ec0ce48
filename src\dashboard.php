<?php

use App\classes\Puesto;
use App\classes\Empleado;
use App\classes\EmpleadoTurno;
use App\classes\Cita;
use App\classes\Servicio;
use App\classes\CitaProgramada;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en lventas.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para asignar un empleado a un puesto
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'asignar_turno') {
    $response = ['success' => false, 'message' => ''];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_puesto']) || !is_numeric($_POST['id_puesto'])) {
            throw new Exception('ID de puesto inválido');
        }

        if (empty($_POST['id_empleado']) || !is_numeric($_POST['id_empleado'])) {
            throw new Exception('ID de empleado inválido');
        }

        $id_puesto   = (int)$_POST['id_puesto'];
        $id_empleado = (int)$_POST['id_empleado'];

        // Establecer la zona horaria para Colombia
        date_default_timezone_set('America/Bogota');

        // Verificar si el empleado ya tiene un turno activo (validación del lado del servidor)
        if (EmpleadoTurno::tiene_turno_activo($id_empleado, $conexion)) {
            // Obtener el nombre del empleado para el mensaje de error
            $empleado = Empleado::get($id_empleado, $conexion);
            $nombre_empleado = $empleado ? $empleado->getNombre() : 'El empleado';
            throw new Exception("$nombre_empleado ya está asignado a otro puesto. No puede estar en dos puestos simultáneamente.");
        }

        // Verificar si ya existe un turno activo para este puesto
        $turno_activo = EmpleadoTurno::get_activo_by_puesto($id_puesto, $conexion);

        if ($turno_activo) {
            // Si hay un turno activo, finalizarlo antes de crear uno nuevo
            $fecha_fin = date('Y-m-d H:i:s');
            EmpleadoTurno::finalizar($turno_activo->getId(), $fecha_fin, $conexion);
        }

        // Crear nuevo turno
        $nuevo_turno = new EmpleadoTurno();
        $nuevo_turno->setFecha_inicio(date('Y-m-d H:i:s'));
        $nuevo_turno->setId_puesto($id_puesto);
        $nuevo_turno->setId_empleado($id_empleado);

        $id_turno = $nuevo_turno->crear($conexion);

        if ($id_turno) {
            // Obtener información del empleado y puesto para la respuesta
            $empleado = Empleado::get($id_empleado, $conexion);
            $puesto = Puesto::get($id_puesto, $conexion);

            $response['success'] = true;
            $response['message'] = 'Turno asignado correctamente';
            $response['id_turno'] = $id_turno;
            $response['nombre_empleado'] = $empleado ? $empleado->getNombre() : 'Desconocido';
            $response['descripcion_puesto'] = $puesto ? $puesto->getDescripcion() : 'Desconocido';
        } else {
            throw new Exception('Error al crear el turno');
        }

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para finalizar un turno
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'finalizar_turno') {
    $response = ['success' => false, 'message' => ''];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_turno']) || !is_numeric($_POST['id_turno'])) {
            throw new Exception('ID de turno inválido');
        }

        $id_turno = (int)$_POST['id_turno'];

        // Establecer la zona horaria para Colombia
        date_default_timezone_set('America/Bogota');
        $fecha_fin = date('Y-m-d H:i:s');

        // Finalizar el turno
        $resultado = EmpleadoTurno::finalizar($id_turno, $fecha_fin, $conexion);

        if ($resultado) {
            $response['success'] = true;
            $response['message'] = 'Turno finalizado correctamente';
        } else {
            throw new Exception('Error al finalizar el turno');
        }

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para validar cierre de caja
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'validar_cierre_caja') {
    $response = ['success' => false, 'message' => '', 'citas_activas' => 0, 'turnos_activos' => 0];

    try {
        // Establecer la zona horaria para Colombia
        date_default_timezone_set('America/Bogota');

        // Verificar que hay un centro de costo seleccionado en la sesión
        if (!isset($_SESSION[CENTRO_COSTO_SESSION]) || empty($_SESSION[CENTRO_COSTO_SESSION])) {
            $response['message'] = 'Error: No hay un centro de costo seleccionado. Por favor, seleccione un centro de costo antes de cerrar la caja.';
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }

        $id_centro_costo_seleccionado = (int)$_SESSION[CENTRO_COSTO_SESSION];

        // Obtener el nombre del centro de costo seleccionado
        // La variable $selected_centro_costo_nombre está disponible desde preparar.php
        $nombre_centro_costo = $selected_centro_costo_nombre ?? 'seleccionado';

        // Usar el método del modelo para contar citas activas
        // Ya no filtramos por fecha - cualquier cita activa impide el cierre
        $citas_activas = Cita::countActiveCitasByCentroCosto($id_centro_costo_seleccionado, $conexion);

        // Usar el método del modelo para contar turnos activos
        $turnos_activos = EmpleadoTurno::countActiveShiftsByCentroCosto($id_centro_costo_seleccionado, $conexion);

        $response['success']       = true;
        $response['citas_activas'] = $citas_activas;
        $response['turnos_activos'] = $turnos_activos;
        $response['nombre_centro_costo'] = $nombre_centro_costo;

        // Validar primero las citas activas
        if ($citas_activas > 0) {
            $response['message'] = "No se puede cerrar la caja. Hay $citas_activas cita(s) en progreso en el centro de costo '$nombre_centro_costo'. Debe finalizar todas las citas antes de cerrar la caja.";
        }
        // Si no hay citas activas, validar los turnos activos
        else if ($turnos_activos > 0) {
            $response['message'] = "No se puede cerrar la caja. Hay $turnos_activos turno(s) activo(s) en el centro de costo '$nombre_centro_costo'. Debe finalizar todos los turnos antes de cerrar la caja.";
        }
        // Si no hay citas ni turnos activos, la caja puede ser cerrada
        else {
            $response['message'] = "No hay citas ni turnos activos en el centro de costo '$nombre_centro_costo'. La caja puede ser cerrada.";
        }

    } catch (Exception $e) {
        $response['message'] = 'Error al validar el estado para cierre de caja: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para obtener turnos activos
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_turnos_activos') {
    $response = ['success' => false, 'message' => '', 'turnos' => []];

    try {
        // Establecer la zona horaria para Colombia
        date_default_timezone_set('America/Bogota');

        // Obtener todos los turnos activos
        $turnos = EmpleadoTurno::get_list_activos($conexion);

        // Preparar los datos para la respuesta, filtrando los turnos que ya tienen citas activas
        $turnos_data = [];
        foreach ($turnos as $turno) {
            // Verificar si el turno ya tiene una cita activa
            if (!$turno->tieneCitaActiva($conexion)) {
                $turnos_data[] = [
                    'id'                 => $turno->getId(),
                    'nombre_empleado'    => $turno->getNombre_empleado(),
                    'descripcion_puesto' => $turno->getDescripcion_puesto(),
                    'fecha_inicio'       => $turno->getFecha_inicio()
                ];
            }
        }

        $response['success'] = true;
        $response['turnos'] = $turnos_data;
        $response['message'] = count($turnos_data) > 0 ? 'Turnos activos disponibles obtenidos correctamente' : 'No hay turnos disponibles para crear citas';

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para obtener servicios activos
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_servicios_activos') {
    $response = ['success' => false, 'message' => '', 'servicios' => []];

    try {
        // Obtener todos los servicios activos
        $servicios = Servicio::get_list($conexion);

        // Preparar los datos para la respuesta
        $servicios_data = [];
        foreach ($servicios as $servicio) {
            $servicios_data[] = [
                'id'          => $servicio->getId(),
                'descripcion' => $servicio->getDescripcion(),
                'valor'       => $servicio->getValor()
            ];
        }

        $response['success']   = true;
        $response['servicios'] = $servicios_data;
        $response['message']   = count($servicios_data) > 0 ? 'Servicios activos obtenidos correctamente' : 'No hay servicios activos disponibles';

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para obtener servicios de un empleado
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_empleado_servicios') {
    $response = ['success' => false, 'message' => '', 'servicios' => []];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_empleado_turno']) || !is_numeric($_POST['id_empleado_turno'])) {
            throw new Exception('ID de turno de empleado inválido');
        }

        $id_empleado_turno = (int)$_POST['id_empleado_turno'];

        // Obtener el turno de empleado
        $turno = EmpleadoTurno::get($id_empleado_turno, $conexion);
        if (!$turno) {
            throw new Exception('El turno de empleado no existe');
        }

        // Obtener el empleado
        $empleado = Empleado::get($turno->getId_empleado(), $conexion);
        if (!$empleado) {
            throw new Exception('El empleado no existe');
        }

        // Obtener los servicios asociados al empleado
        $servicios = $empleado->getServicios($conexion);

        // Preparar los datos para la respuesta
        $servicios_data = [];
        foreach ($servicios as $servicio) {
            $servicios_data[] = [
                'id'          => $servicio->getId(),
                'descripcion' => $servicio->getDescripcion(),
                'valor'       => $servicio->getValor()
            ];
        }

        $response['success']   = true;
        $response['servicios'] = $servicios_data;
        $response['empleado']  = $empleado->getNombre();
        $response['message']   = count($servicios_data) > 0 ? 'Servicios del empleado obtenidos correctamente' : 'El empleado no tiene servicios asociados';

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para crear una cita
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'crear_cita') {
    $response = ['success' => false, 'message' => ''];

    try {
        // Establecer la zona horaria para Colombia
        date_default_timezone_set('America/Bogota');

        // Validar datos de entrada
        if (empty($_POST['id_empleado_turno']) || !is_numeric($_POST['id_empleado_turno'])) {
            throw new Exception('ID de turno de empleado inválido');
        }

        if (empty($_POST['servicios'])) {
            throw new Exception('Debe seleccionar al menos un servicio');
        }

        $id_empleado_turno = (int)$_POST['id_empleado_turno'];

        // Verificar que el turno no tenga ya una cita activa
        if (EmpleadoTurno::tieneCitaActivaById($id_empleado_turno, $conexion)) {
            throw new Exception('El turno seleccionado ya tiene una cita activa. Por favor, seleccione otro turno.');
        }

        // Establecer la zona horaria para Colombia
        date_default_timezone_set('America/Bogota');
        $fecha_inicio = date('Y-m-d H:i:s');
        $fecha_fin = null; // La fecha_fin será NULL hasta que la cita sea finalizada

        $servicios = json_decode($_POST['servicios'], true);

        if (!is_array($servicios) || count($servicios) === 0) {
            throw new Exception('Debe seleccionar al menos un servicio');
        }

        // Extraer IDs de servicios para validación
        $serviciosIds = array_map(function($servicio) {
            return (int)$servicio['id'];
        }, $servicios);

        // Iniciar transacción
        $conexion->beginTransaction();

        // Crear la cita con validación de servicios
        $cita = new Cita();
        $cita->setId_empleado_turno($id_empleado_turno);
        $cita->setFecha_inicio($fecha_inicio);
        $cita->setFecha_fin($fecha_fin);

        $id_cita = $cita->crear($conexion, $serviciosIds);

        if (!$id_cita) {
            throw new Exception('Error al crear la cita');
        }

        // Establecer el ID de la cita creada
        $cita->setId($id_cita);

        // Agregar los servicios a la cita
        foreach ($servicios as $servicio) {
            $id_servicio = (int)$servicio['id'];
            $valor       = (float)$servicio['valor'];

            // Verificar que el servicio exista
            $servicio_obj = Servicio::get($id_servicio, $conexion);
            if (!$servicio_obj) {
                throw new Exception("El servicio con ID $id_servicio no existe");
            }

            // Agregar el servicio a la cita
            $resultado = $cita->agregarServicio($id_servicio, $valor, $conexion);
            if (!$resultado) {
                throw new Exception("Error al agregar el servicio con ID $id_servicio a la cita");
            }
        }

        // Si la cita se creó desde una cita programada, marcarla como realizada
        if (!empty($_POST['id_cita_programada']) && is_numeric($_POST['id_cita_programada'])) {
            $id_cita_programada = (int)$_POST['id_cita_programada'];
            $cita_programada    = CitaProgramada::get($id_cita_programada, $conexion);

            if ($cita_programada) {
                $cita_programada->marcarComoRealizada($conexion);
            }
        }

        // Confirmar la transacción
        $conexion->commit();

        // Preparar la respuesta
        $response['success'] = true;
        $response['message'] = 'Cita creada correctamente';
        $response['id_cita'] = $id_cita;

    } catch (Exception $e) {
        // Revertir la transacción en caso de error
        if ($conexion->inTransaction()) {
            $conexion->rollBack();
        }

        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para obtener los servicios de una cita
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_servicios_cita') {
    $response = ['success' => false, 'message' => '', 'servicios' => [], 'total' => 0];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
            throw new Exception('ID de cita inválido');
        }

        $id_cita = (int)$_POST['id_cita'];

        // Obtener la cita
        $cita = Cita::get($id_cita, $conexion);
        if (!$cita) {
            throw new Exception('La cita no existe');
        }

        // Obtener los servicios de la cita
        $servicios = $cita->getServicios($conexion);
        $total     = $cita->getTotalValorServicios($conexion);

        // Obtener información del empleado y puesto
        $turno = EmpleadoTurno::get($cita->getId_empleado_turno(), $conexion);
        if (!$turno) {
            throw new Exception('El turno asociado a la cita no existe');
        }

        // Preparar los datos para la respuesta
        $servicios_data = [];
        foreach ($servicios as $servicio) {
            $servicios_data[] = [
                'id' => $servicio->getId(),
                'descripcion' => $servicio->getDescripcion(),
                'valor' => $servicio->getValor()
            ];
        }

        $response['success']            = true;
        $response['servicios']          = $servicios_data;
        $response['total']              = $total;
        $response['fecha_inicio']       = date('Y-m-d H:i', strtotime($cita->getFecha_inicio()));
        $response['id_empleado_turno']  = $cita->getId_empleado_turno();
        $response['nombre_empleado']    = $turno->getNombre_empleado();
        $response['descripcion_puesto'] = $turno->getDescripcion_puesto();
        $response['message']            = 'Servicios de la cita obtenidos correctamente';

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para obtener los servicios disponibles para agregar a una cita
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_servicios_disponibles') {
    $response = ['success' => false, 'message' => '', 'servicios' => []];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
            throw new Exception('ID de cita inválido');
        }

        $id_cita = (int)$_POST['id_cita'];

        // Obtener la cita
        $cita = Cita::get($id_cita, $conexion);
        if (!$cita) {
            throw new Exception('La cita no existe');
        }

        // Obtener los servicios actuales de la cita
        $servicios_cita = $cita->getServicios($conexion);
        $ids_servicios_cita = array_map(function($servicio) {
            return $servicio->getId();
        }, $servicios_cita);

        // Obtener todos los servicios activos
        $todos_servicios = Servicio::get_list($conexion);

        // Filtrar los servicios que no están en la cita
        $servicios_disponibles = array_filter($todos_servicios, function($servicio) use ($ids_servicios_cita) {
            return !in_array($servicio->getId(), $ids_servicios_cita);
        });

        // Preparar los datos para la respuesta
        $servicios_data = [];
        foreach ($servicios_disponibles as $servicio) {
            $servicios_data[] = [
                'id'          => $servicio->getId(),
                'descripcion' => $servicio->getDescripcion(),
                'valor'       => $servicio->getValor()
            ];
        }

        $response['success']   = true;
        $response['servicios'] = $servicios_data;
        $response['message']   = count($servicios_data) > 0 ? 'Servicios disponibles obtenidos correctamente' : 'No hay servicios adicionales disponibles';

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para eliminar un servicio de una cita
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'eliminar_servicio_cita') {
    $response = ['success' => false, 'message' => ''];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
            throw new Exception('ID de cita inválido');
        }

        if (empty($_POST['id_servicio']) || !is_numeric($_POST['id_servicio'])) {
            throw new Exception('ID de servicio inválido');
        }

        $id_cita = (int)$_POST['id_cita'];
        $id_servicio = (int)$_POST['id_servicio'];

        // Obtener la cita
        $cita = Cita::get($id_cita, $conexion);
        if (!$cita) {
            throw new Exception('La cita no existe');
        }

        // Verificar que la cita tenga más de un servicio antes de eliminar
        $servicios = $cita->getServicios($conexion);
        if (count($servicios) <= 1) {
            throw new Exception('La cita debe tener al menos un servicio. No se puede eliminar el último servicio.');
        }

        // Iniciar transacción
        $conexion->beginTransaction();

        // Eliminar el servicio de la cita
        $resultado = $cita->eliminarServicio($id_servicio, $conexion);
        if (!$resultado) {
            throw new Exception('Error al eliminar el servicio de la cita');
        }

        // Confirmar la transacción
        $conexion->commit();

        // Obtener la información actualizada de la cita para actualizar la tarjeta en el dashboard
        $cita                   = Cita::get($id_cita, $conexion);            // Recargar la cita para obtener datos actualizados
        $servicios_actualizados = $cita->getServicios($conexion);
        $total_actualizado      = $cita->getTotalValorServicios($conexion);

        // Obtener el turno de empleado asociado a la cita
        $id_empleado_turno = $cita->getId_empleado_turno();
        $empleado_turno    = EmpleadoTurno::get($id_empleado_turno, $conexion);

        // Preparar los datos de servicios para la respuesta
        $servicios_data = [];
        foreach ($servicios_actualizados as $servicio) {
            $servicios_data[] = [
                'id'          => $servicio->getId(),
                'descripcion' => $servicio->getDescripcion(),
                'valor'       => $servicio->getValor()
            ];
        }

        // Preparar la respuesta
        $response['success']           = true;
        $response['message']           = 'Servicio eliminado correctamente de la cita';
        $response['id_empleado_turno'] = $id_empleado_turno;
        $response['servicios']         = $servicios_data;
        $response['total']             = $total_actualizado;
        $response['nombre_empleado']   = $empleado_turno->getNombre_empleado();
        $response['id_puesto']         = $empleado_turno->getId_puesto();

    } catch (Exception $e) {
        // Revertir la transacción en caso de error
        if ($conexion->inTransaction()) {
            $conexion->rollBack();
        }

        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para agregar servicios a una cita
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'agregar_servicios_cita') {
    $response = ['success' => false, 'message' => ''];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
            throw new Exception('ID de cita inválido');
        }

        if (empty($_POST['servicios'])) {
            throw new Exception('Debe seleccionar al menos un servicio para agregar');
        }

        $id_cita = (int)$_POST['id_cita'];
        $servicios = json_decode($_POST['servicios'], true);

        if (!is_array($servicios) || count($servicios) === 0) {
            throw new Exception('Debe seleccionar al menos un servicio para agregar');
        }

        // Obtener la cita
        $cita = Cita::get($id_cita, $conexion);
        if (!$cita) {
            throw new Exception('La cita no existe');
        }

        // Extraer IDs de servicios para validación
        $serviciosIds = array_map(function($servicio) {
            return (int)$servicio['id'];
        }, $servicios);

        // Validar que el empleado puede realizar todos los servicios nuevos
        $empleadoTurno = EmpleadoTurno::get($cita->getId_empleado_turno(), $conexion);
        if (!$empleadoTurno) {
            throw new Exception('No se pudo obtener información del turno del empleado.');
        }

        $empleado = Empleado::get($empleadoTurno->getId_empleado(), $conexion);
        if (!$empleado) {
            throw new Exception('No se pudo obtener información del empleado.');
        }

        $validacion = $empleado->validarServiciosAsociados($serviciosIds, $conexion);
        if (!$validacion['valido']) {
            $serviciosFaltantes = $validacion['servicios_faltantes'];
            $nombresServicios = Empleado::obtenerNombresServicios($serviciosFaltantes, $conexion);

            $serviciosTexto = [];
            foreach ($serviciosFaltantes as $idServicio) {
                $serviciosTexto[] = $nombresServicios[$idServicio] ?? "Servicio ID: $idServicio";
            }

            $mensaje = "El empleado '{$empleado->getNombre()}' no puede realizar " .
                      (count($serviciosTexto) === 1 ? "el servicio" : "los servicios") . ": " .
                      implode(", ", $serviciosTexto) . ". " .
                      "Por favor, seleccione servicios que el empleado pueda realizar.";

            throw new Exception($mensaje);
        }

        // Iniciar transacción
        $conexion->beginTransaction();

        // Agregar los servicios a la cita
        foreach ($servicios as $servicio) {
            $id_servicio = (int)$servicio['id'];
            $valor = (float)$servicio['valor'];

            // Verificar que el servicio exista
            $servicio_obj = Servicio::get($id_servicio, $conexion);
            if (!$servicio_obj) {
                throw new Exception("El servicio con ID $id_servicio no existe");
            }

            // Agregar el servicio a la cita
            $resultado = $cita->agregarServicio($id_servicio, $valor, $conexion);
            if (!$resultado) {
                throw new Exception("Error al agregar el servicio con ID $id_servicio a la cita");
            }
        }

        // Confirmar la transacción
        $conexion->commit();

        // Obtener la información actualizada de la cita para actualizar la tarjeta en el dashboard
        $cita = Cita::get($id_cita, $conexion); // Recargar la cita para obtener datos actualizados
        $servicios_actualizados = $cita->getServicios($conexion);
        $total_actualizado = $cita->getTotalValorServicios($conexion);

        // Obtener el turno de empleado asociado a la cita
        $id_empleado_turno = $cita->getId_empleado_turno();
        $empleado_turno = EmpleadoTurno::get($id_empleado_turno, $conexion);

        // Preparar los datos de servicios para la respuesta
        $servicios_data = [];
        foreach ($servicios_actualizados as $servicio) {
            $servicios_data[] = [
                'id'          => $servicio->getId(),
                'descripcion' => $servicio->getDescripcion(),
                'valor'       => $servicio->getValor()
            ];
        }

        // Preparar la respuesta
        $response['success']           = true;
        $response['message']           = 'Servicios agregados correctamente a la cita';
        $response['id_empleado_turno'] = $id_empleado_turno;
        $response['servicios']         = $servicios_data;
        $response['total']             = $total_actualizado;
        $response['nombre_empleado']   = $empleado_turno->getNombre_empleado();
        $response['id_puesto']         = $empleado_turno->getId_puesto();

    } catch (Exception $e) {
        // Revertir la transacción en caso de error
        if ($conexion->inTransaction()) {
            $conexion->rollBack();
        }

        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para eliminar múltiples servicios de una cita
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'eliminar_servicios_cita') {
    $response = ['success' => false, 'message' => ''];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
            throw new Exception('ID de cita inválido');
        }

        if (empty($_POST['servicios'])) {
            throw new Exception('No se han seleccionado servicios para eliminar');
        }

        $id_cita = (int)$_POST['id_cita'];
        $servicios = json_decode($_POST['servicios'], true);

        if (!is_array($servicios) || count($servicios) === 0) {
            throw new Exception('No se han seleccionado servicios para eliminar');
        }

        // Obtener la cita
        $cita = Cita::get($id_cita, $conexion);
        if (!$cita) {
            throw new Exception('La cita no existe');
        }

        // Verificar que la cita tenga más servicios que los que se van a eliminar
        $servicios_actuales = $cita->getServicios($conexion);
        if (count($servicios_actuales) <= count($servicios)) {
            throw new Exception('La cita debe tener al menos un servicio. No se pueden eliminar todos los servicios.');
        }

        // Iniciar transacción
        $conexion->beginTransaction();

        // Eliminar los servicios de la cita
        $servicios_eliminados = 0;
        foreach ($servicios as $id_servicio) {
            $id_servicio = (int)$id_servicio;

            // Verificar que el servicio exista en la cita
            $existe_servicio = false;
            foreach ($servicios_actuales as $servicio_actual) {
                if ($servicio_actual->getId() === $id_servicio) {
                    $existe_servicio = true;
                    break;
                }
            }

            if (!$existe_servicio) {
                continue; // Saltar si el servicio no existe en la cita
            }

            // Eliminar el servicio de la cita
            $resultado = $cita->eliminarServicio($id_servicio, $conexion);
            if ($resultado) {
                $servicios_eliminados++;
            }
        }

        // Verificar que se hayan eliminado servicios
        if ($servicios_eliminados === 0) {
            throw new Exception('No se pudo eliminar ningún servicio de la cita');
        }

        // Confirmar la transacción
        $conexion->commit();

        // Obtener la información actualizada de la cita para actualizar la tarjeta en el dashboard
        $cita                   = Cita::get($id_cita, $conexion);            // Recargar la cita para obtener datos actualizados
        $servicios_actualizados = $cita->getServicios($conexion);
        $total_actualizado      = $cita->getTotalValorServicios($conexion);

        // Obtener el turno de empleado asociado a la cita
        $id_empleado_turno = $cita->getId_empleado_turno();
        $empleado_turno    = EmpleadoTurno::get($id_empleado_turno, $conexion);

        // Preparar los datos de servicios para la respuesta
        $servicios_data = [];
        foreach ($servicios_actualizados as $servicio) {
            $servicios_data[] = [
                'id'          => $servicio->getId(),
                'descripcion' => $servicio->getDescripcion(),
                'valor'       => $servicio->getValor()
            ];
        }

        // Preparar la respuesta
        $response['success'] = true;
        $response['message'] = $servicios_eliminados > 1
            ? "Se eliminaron $servicios_eliminados servicios correctamente de la cita"
            : "Se eliminó 1 servicio correctamente de la cita";
        $response['id_empleado_turno'] = $id_empleado_turno;
        $response['servicios']         = $servicios_data;
        $response['total']             = $total_actualizado;
        $response['nombre_empleado']   = $empleado_turno->getNombre_empleado();
        $response['id_puesto']         = $empleado_turno->getId_puesto();

    } catch (Exception $e) {
        // Revertir la transacción en caso de error
        if ($conexion->inTransaction()) {
            $conexion->rollBack();
        }

        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para verificar si una cita corresponde a un turno de empleado
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'verificar_cita_turno') {
    $response = ['success' => false, 'coincide' => false];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
            throw new Exception('ID de cita inválido');
        }

        if (empty($_POST['id_empleado_turno']) || !is_numeric($_POST['id_empleado_turno'])) {
            throw new Exception('ID de turno de empleado inválido');
        }

        $id_cita = (int)$_POST['id_cita'];
        $id_empleado_turno = (int)$_POST['id_empleado_turno'];

        // Obtener la cita
        $cita = Cita::get($id_cita, $conexion);
        if (!$cita) {
            throw new Exception('La cita no existe');
        }

        // Verificar si la cita corresponde al turno del empleado
        $coincide = ($cita->getId_empleado_turno() === $id_empleado_turno);

        // Preparar la respuesta
        $response['success'] = true;
        $response['coincide'] = $coincide;

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para validar servicios del empleado
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'validar_servicios_empleado') {
    $response = ['success' => false, 'message' => '', 'valido' => false];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_empleado_turno']) || !is_numeric($_POST['id_empleado_turno'])) {
            throw new Exception('ID de turno de empleado inválido');
        }

        if (empty($_POST['servicios_ids'])) {
            throw new Exception('No se han especificado servicios para validar');
        }

        $id_empleado_turno = (int)$_POST['id_empleado_turno'];
        $servicios_ids = json_decode($_POST['servicios_ids'], true);

        if (!is_array($servicios_ids) || count($servicios_ids) === 0) {
            throw new Exception('Lista de servicios inválida');
        }

        // Convertir a enteros
        $servicios_ids = array_map('intval', $servicios_ids);

        // Obtener el empleado del turno
        $empleadoTurno = EmpleadoTurno::get($id_empleado_turno, $conexion);
        if (!$empleadoTurno) {
            throw new Exception('No se pudo obtener información del turno del empleado.');
        }

        $empleado = Empleado::get($empleadoTurno->getId_empleado(), $conexion);
        if (!$empleado) {
            throw new Exception('No se pudo obtener información del empleado.');
        }

        // Validar servicios
        $validacion = $empleado->validarServiciosAsociados($servicios_ids, $conexion);

        if ($validacion['valido']) {
            $response['success'] = true;
            $response['valido'] = true;
            $response['message'] = 'El empleado puede realizar todos los servicios seleccionados.';
        } else {
            // Obtener nombres de servicios faltantes
            $serviciosFaltantes = $validacion['servicios_faltantes'];
            $nombresServicios = Empleado::obtenerNombresServicios($serviciosFaltantes, $conexion);

            $serviciosTexto = [];
            foreach ($serviciosFaltantes as $idServicio) {
                $serviciosTexto[] = $nombresServicios[$idServicio] ?? "Servicio ID: $idServicio";
            }

            $response['success'] = true;
            $response['valido'] = false;
            $response['message'] = "El empleado '{$empleado->getNombre()}' no puede realizar " .
                                  (count($serviciosTexto) === 1 ? "el servicio" : "los servicios") . ": " .
                                  implode(", ", $serviciosTexto) . ". " .
                                  "Por favor, seleccione un empleado diferente o servicios que el empleado pueda realizar.";
        }

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para obtener empleados activos
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_empleados_activos') {
    $response = ['success' => false, 'message' => '', 'empleados' => []];

    try {
        // Obtener todos los empleados activos
        $empleados = Empleado::get_list($conexion);

        // Preparar los datos para la respuesta
        $empleados_data = [];
        foreach ($empleados as $empleado) {
            $empleados_data[] = [
                'id' => $empleado->getId(),
                'nombre' => $empleado->getNombre()
            ];
        }

        $response['success'] = true;
        $response['empleados'] = $empleados_data;
        $response['message'] = count($empleados_data) > 0 ? 'Empleados activos obtenidos correctamente' : 'No hay empleados activos disponibles';

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para obtener citas programadas
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_citas_programadas') {
    $response = ['success' => false, 'message' => '', 'citas' => []];

    try {
        // Construir filtros
        $filtros = [];

        if (!empty($_POST['fecha'])) {
            $fecha                   = $_POST['fecha'];
            $filtros['fecha_inicio'] = $fecha . ' 00:00:00';
            $filtros['fecha_fin']    = $fecha . ' 23:59:59';
        }

        if (!empty($_POST['id_empleado']) && is_numeric($_POST['id_empleado'])) {
            $filtros['id_empleado'] = (int)$_POST['id_empleado'];
        }

        if (!empty($_POST['filtro_cliente'])) {
            $filtros['filtro_cliente'] = trim($_POST['filtro_cliente']);
        }

        // Obtener citas programadas con filtros (solo las no realizadas)
        $citas_programadas = CitaProgramada::get_list_filtered($conexion, $filtros, false);

        // Filtrar solo las que no están realizadas
        $citas_no_realizadas = array_filter($citas_programadas, function($cita) {
            return $cita->getEs_realizado() == 0;
        });

        // Preparar los datos para la respuesta
        $citas_data = [];
        foreach ($citas_no_realizadas as $cita) {
            // Obtener servicios de la cita programada
            $servicios = $cita->getServicios($conexion);
            $servicios_data = [];
            $total = 0;

            foreach ($servicios as $servicio) {
                $servicios_data[] = [
                    'id'          => $servicio->getId(),
                    'descripcion' => $servicio->getDescripcion(),
                    'valor'       => $servicio->getValor()
                ];
                $total += $servicio->getValor();
            }

            $citas_data[] = [
                'id'              => $cita->getId(),
                'fecha_inicio'    => $cita->getFecha_inicio(),
                'fecha_fin'       => $cita->getFecha_fin(),
                'id_empleado'     => $cita->getId_empleado(),
                'id_cliente'      => $cita->getId_cliente(),
                'nombre_empleado' => $cita->getNombre_empleado(),
                'nombre_cliente'  => $cita->getNombre_cliente(),
                'celular_cliente' => $cita->getCelular_cliente(),
                'servicios'       => $servicios_data,
                'total'           => $total,
                'duracion'        => $cita->getDuracion()
            ];
        }

        $response['success'] = true;
        $response['citas'] = $citas_data;
        $response['message'] = count($citas_data) > 0 ? 'Citas programadas obtenidas correctamente' : 'No hay citas programadas con los filtros aplicados';

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Verificar si es una solicitud AJAX para cancelar una cita programada
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'cancelar_cita_programada') {
    $response = ['success' => false, 'message' => ''];

    try {
        // Validar datos de entrada
        if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
            throw new Exception('ID de cita programada inválido');
        }

        $id_cita = (int)$_POST['id_cita'];

        // Obtener la cita programada
        $cita = CitaProgramada::get($id_cita, $conexion);
        if (!$cita) {
            throw new Exception('La cita programada no existe');
        }

        // Get cancellation reason
        $razon_cancelacion = !empty($_POST['razon_cancelacion']) ? trim($_POST['razon_cancelacion']) : 'Cancelada desde dashboard';

        // Cancelar la cita programada (cambiar estado a 0)
        $resultado = $cita->cancelar($razon_cancelacion, $conexion);

        if ($resultado) {
            $response['success'] = true;
            $response['message'] = 'Cita programada cancelada correctamente';
        } else {
            throw new Exception('Error al cancelar la cita programada');
        }

    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    // Devolver respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Obtener datos para la vista
try {
    // Obtener todos los puestos activos
    $puestos = Puesto::get_list($conexion);

    // Obtener solo los empleados disponibles (sin turnos activos)
    $empleados = EmpleadoTurno::get_empleados_disponibles($conexion);

    // Para cada puesto, verificar si hay un turno activo
    foreach ($puestos as $puesto) {
        $turno_activo = EmpleadoTurno::get_activo_by_puesto($puesto->getId(), $conexion);

        // Si hay un turno activo, verificar si tiene una cita activa
        if ($turno_activo) {
            $tiene_cita_activa = $turno_activo->tieneCitaActiva($conexion);
            $turno_activo->tiene_cita_activa = $tiene_cita_activa;

            // Si tiene una cita activa, obtener los detalles de la cita y sus servicios
            if ($tiene_cita_activa) {
                // Obtener la cita activa para este turno
                $query = "SELECT id FROM citas WHERE id_empleado_turno = :id_turno AND fecha_fin IS NULL AND estado = 1 LIMIT 1";
                $statement = $conexion->prepare($query);
                $statement->bindValue(':id_turno', $turno_activo->getId(), PDO::PARAM_INT);
                $statement->execute();
                $resultado = $statement->fetch(PDO::FETCH_ASSOC);

                if ($resultado) {
                    $id_cita = $resultado['id'];
                    $cita = Cita::get($id_cita, $conexion, true); // Incluir citas canceladas para evitar errores

                    if ($cita) {
                        // Obtener los servicios asociados a la cita
                        $servicios_cita = $cita->getServicios($conexion);
                        $total_servicios = $cita->getTotalValorServicios($conexion);

                        // Guardar la información en el objeto turno_activo
                        $turno_activo->cita_activa = $cita;
                        $turno_activo->servicios_cita = $servicios_cita;
                        $turno_activo->total_servicios = $total_servicios;
                    }
                }
            }
        }

        $puesto->turno_activo = $turno_activo;
    }

} catch (Exception $e) {
    $_SESSION['flash_message_error'] = 'Error al cargar datos: ' . $e->getMessage();
}

require_once __ROOT__ . '/views/dashboard.view.php';

?>