<?php

declare(strict_types=1);

// Incluir clases necesarias
use App\classes\Cita;
use App\classes\CentroCosto;
use App\classes\Empleado;
use App\classes\Puesto;
use App\classes\MetodoPago;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lmetodos_pagos.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Configuración de zona horaria
date_default_timezone_set('America/Bogota');

// Variables para la vista
$error_display       = 'hide';
$error_text          = '';
$success_display     = 'hide';
$success_text        = '';
$centros_costos      = [];
$empleados           = [];
$puestos             = [];
$metodos_pagos       = [];
$consulta_data       = null;
$fecha_inicio        = '';
$fecha_fin           = '';
$id_centro_costo     = null;
$centro_costo_nombre = '';

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    #region Search Citas
    if (isset($_POST['action']) && $_POST['action'] === 'search_citas') {
        try {
            // Validar campos obligatorios
            $fecha_inicio = trim($_POST['fecha_inicio'] ?? '');
            $fecha_fin    = trim($_POST['fecha_fin'] ?? '');

            // Get centro de costo from session (set in preparar.php)
            $id_centro_costo = isset($_SESSION[CENTRO_COSTO_SESSION]) ? (int)$_SESSION[CENTRO_COSTO_SESSION] : null;

            if (empty($fecha_inicio) || empty($fecha_fin)) {
                throw new Exception("Los campos fecha de inicio y fecha de fin son obligatorios.");
            }

            if ($id_centro_costo === null) {
                throw new Exception("No se ha seleccionado un centro de costo válido. Por favor, seleccione un centro de costo desde el menú lateral.");
            }

            // Validar formato de fechas
            if (!DateTime::createFromFormat('Y-m-d', $fecha_inicio) || !DateTime::createFromFormat('Y-m-d', $fecha_fin)) {
                throw new Exception("Las fechas deben tener el formato YYYY-MM-DD.");
            }

            // Validar que fecha inicio <= fecha fin
            if ($fecha_inicio > $fecha_fin) {
                throw new Exception("La fecha de inicio no puede ser mayor que la fecha de fin.");
            }

            // Validar que el centro de costo existe
            $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
            if (!$centro_costo) {
                throw new Exception("El centro de costo seleccionado no existe.");
            }
            $centro_costo_nombre = $centro_costo->getNombre();

            // Capturar filtros opcionales
            $id_empleado     = !empty($_POST['id_empleado']) ? (int)$_POST['id_empleado'] : null;
            $id_puesto       = !empty($_POST['id_puesto']) ? (int)$_POST['id_puesto'] : null;
            $id_metodo_pago  = !empty($_POST['id_metodo_pago']) ? (int)$_POST['id_metodo_pago'] : null;

            // Validar que los filtros opcionales existen si se proporcionan
            if ($id_empleado !== null) {
                $empleado = Empleado::get($id_empleado, $conexion);
                if (!$empleado) {
                    throw new Exception("El empleado seleccionado no existe.");
                }
            }

            if ($id_puesto !== null) {
                $puesto = Puesto::get($id_puesto, $conexion);
                if (!$puesto) {
                    throw new Exception("El puesto seleccionado no existe.");
                }
            }

            if ($id_metodo_pago !== null) {
                $metodo_pago = MetodoPago::get($id_metodo_pago, $conexion);
                if (!$metodo_pago) {
                    throw new Exception("El método de pago seleccionado no existe.");
                }
            }

            // Crear array de filtros adicionales
            $filtros_adicionales = [
                'id_empleado'    => $id_empleado,
                'id_puesto'      => $id_puesto,
                'id_metodo_pago' => $id_metodo_pago
            ];

            // Generar datos de la consulta usando el método de la clase Cita con filtros adicionales
            $consulta_data = Cita::get_historico_by_fecha_fin_rango($fecha_inicio, $fecha_fin, $id_centro_costo, $conexion, true, $filtros_adicionales);

            if (empty($consulta_data['citas'])) {
                $error_display = 'show';
                $error_text    = 'No se encontraron citas para los criterios de búsqueda seleccionados.';
            } elseif (isset($consulta_data['schema_warning']) && $consulta_data['schema_warning']) {
                $success_display = 'show';
                $success_text    = 'Consulta generada exitosamente. NOTA: El filtro por centro de costo no está disponible debido a la estructura actual de la base de datos. Se muestran todas las citas del rango de fechas seleccionado.';
            }

        } catch (Exception $e) {
            $error_display = 'show';
            $error_text    = 'Error al generar la consulta: ' . $e->getMessage();
        }

        // Responder con JSON para AJAX
        header('Content-Type: application/json');
        echo json_encode([
            'success'        => $error_display !== 'show',
            'message'        => $error_display === 'show' ? $error_text : ($success_text ?: 'Consulta generada exitosamente.'),
            'citas'          => $consulta_data['citas'] ?? [],
            'schema_warning' => $consulta_data['schema_warning'] ?? false
        ]);
        exit;
    }
    #endregion Search Citas

    #region Get Cita Services
    if (isset($_POST['action']) && $_POST['action'] === 'get_cita_services') {
        try {
            $cita_id = !empty($_POST['cita_id']) ? (int)$_POST['cita_id'] : null;

            if ($cita_id === null) {
                throw new Exception("ID de cita requerido.");
            }

            // Obtener la cita
            $cita = Cita::get($cita_id, $conexion, true); // incluir canceladas
            if (!$cita) {
                throw new Exception("La cita no existe.");
            }

            // Obtener los servicios de la cita
            $servicios = $cita->getServicios($conexion);

            // Formatear los servicios para la respuesta
            $servicios_formateados = [];
            foreach ($servicios as $servicio) {
                $servicios_formateados[] = [
                    'id'               => $servicio->getId(),
                    'descripcion'      => $servicio->getDescripcion(),
                    'valor'            => $servicio->getValor(),
                    'valor_formateado' => '$' . number_format($servicio->getValor(), 0, ',', '.')
                ];
            }

            // Calcular total
            $total_valor = $cita->getTotalValorServicios($conexion);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'cita' => [
                    'id'                 => $cita->getId(),
                    'descripcion_puesto' => $cita->getDescripcion_puesto(),
                    'nombre_empleado'    => $cita->getNombre_empleado(),
                    'nombre_metodo_pago' => $cita->getNombre_metodo_pago(),
                    'fecha_inicio'       => $cita->getFecha_inicio(),
                    'fecha_fin'          => $cita->getFecha_fin(),
                    'estado'             => $cita->getEstado(),
                    'razon_cancelacion'  => $cita->getRazon_cancelacion()
                ],
                'servicios'              => $servicios_formateados,
                'total_valor'            => $total_valor,
                'total_valor_formateado' => '$' . number_format($total_valor, 0, ',', '.')
            ]);
            exit;

        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    #endregion Get Cita Services

    #region Export Excel
    if (isset($_POST['action']) && $_POST['action'] === 'exportar_excel') {
        $fecha_inicio = trim($_POST['fecha_inicio'] ?? '');
        $fecha_fin    = trim($_POST['fecha_fin'] ?? '');

        // Get centro de costo from session (set in preparar.php)
        $id_centro_costo = isset($_SESSION[CENTRO_COSTO_SESSION]) ? (int)$_SESSION[CENTRO_COSTO_SESSION] : null;

        // Validaciones
        if (empty($fecha_inicio) || empty($fecha_fin)) {
            $error_display = 'show';
            $error_text    = 'Los campos fecha de inicio y fecha de fin son obligatorios para exportar.';
        } elseif ($id_centro_costo === null) {
            $error_display = 'show';
            $error_text    = 'No se ha seleccionado un centro de costo válido. Por favor, seleccione un centro de costo desde el menú lateral.';
        } else {
            try {
                // Validar formato de fechas
                if (!DateTime::createFromFormat('Y-m-d', $fecha_inicio) || !DateTime::createFromFormat('Y-m-d', $fecha_fin)) {
                    throw new Exception("Las fechas deben tener el formato YYYY-MM-DD.");
                }

                // Validar que fecha inicio <= fecha fin
                if ($fecha_inicio > $fecha_fin) {
                    throw new Exception("La fecha de inicio no puede ser mayor que la fecha de fin.");
                }

                // Validar que el centro de costo existe
                $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
                if (!$centro_costo) {
                    throw new Exception("El centro de costo seleccionado no existe.");
                }
                $centro_costo_nombre = $centro_costo->getNombre();

                // Capturar filtros opcionales
                $id_empleado     = !empty($_POST['id_empleado']) ? (int)$_POST['id_empleado'] : null;
                $id_puesto       = !empty($_POST['id_puesto']) ? (int)$_POST['id_puesto'] : null;
                $id_metodo_pago  = !empty($_POST['id_metodo_pago']) ? (int)$_POST['id_metodo_pago'] : null;

                // Crear array de filtros adicionales
                $filtros_adicionales = [
                    'id_empleado'    => $id_empleado,
                    'id_puesto'      => $id_puesto,
                    'id_metodo_pago' => $id_metodo_pago
                ];

                // Generar datos de la consulta usando el mismo método que la búsqueda
                $consulta_data = Cita::get_historico_by_fecha_fin_rango($fecha_inicio, $fecha_fin, $id_centro_costo, $conexion, true, $filtros_adicionales);

                // Log para debugging
                error_log("Excel Export - Datos obtenidos: " . count($consulta_data['citas'] ?? []) . " citas");

                if (empty($consulta_data['citas'])) {
                    $error_display = 'show';
                    $error_text    = 'No se encontraron datos para exportar en el rango de fechas y centro de costo seleccionados.';
                } else {
                    // Limpiar cualquier salida previa antes de generar Excel
                    while (ob_get_level()) {
                        ob_end_clean();
                    }

                    // Generar y descargar el archivo Excel
                    generarExcelCitasHistorico($consulta_data, $fecha_inicio, $fecha_fin, $centro_costo_nombre);
                    exit; // Terminar la ejecución después de la descarga
                }

            } catch (Exception $e) {
                $error_display = 'show';
                $error_text    = 'Error al exportar el reporte: ' . $e->getMessage();
            }
        }
    }
    #endregion Export Excel
}
#endregion Handle POST Actions

#region Get Initial Data
try {
    // Obtener lista de centros de costo activos
    $centros_costos = CentroCosto::get_list($conexion);

    // Obtener lista de empleados activos
    $empleados = Empleado::get_list($conexion);

    // Obtener lista de puestos activos
    $puestos = Puesto::get_list($conexion);

    // Obtener lista de métodos de pago activos
    $metodos_pagos = MetodoPago::get_list($conexion);

} catch (PDOException $e) {
    $error_display = 'show';
    $error_text    = "Error de base de datos al obtener los datos iniciales.";
} catch (Exception $e) {
    $error_display = 'show';
    $error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion Get Initial Data

/**
 * Función para generar archivo Excel del histórico de citas
 */
function generarExcelCitasHistorico(array $consulta_data, string $fecha_inicio, string $fecha_fin, string $centro_costo_nombre): void
{
    try {
        // Log para debugging
        error_log("Iniciando generación de Excel con " . count($consulta_data['citas']) . " citas");

        // Crear nuevo spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Configurar propiedades del documento
        $spreadsheet->getProperties()
            ->setCreator('Sistema de Gestión de Barbería')
            ->setTitle('Consulta Histórica de Citas')
            ->setSubject('Consulta Histórica de Citas')
            ->setDescription('Reporte detallado del histórico de citas finalizadas');

        // Configurar zona horaria
        date_default_timezone_set('America/Bogota');

        // Variables para el diseño
        $row = 1;

        // METADATA DEL REPORTE
        $sheet->setCellValue('A' . $row, 'CONSULTA HISTÓRICA DE CITAS');
        $sheet->mergeCells('A' . $row . ':G' . $row);
        $sheet->getStyle('A' . $row)->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $row++;

        // Header section with borders - split into field name and value columns
        $headerStartRow = $row;

        // Fecha de generación
        $sheet->setCellValue('A' . $row, 'Fecha de generación:');
        $sheet->setCellValue('B' . $row, date('Y-m-d H:i:s'));
        $sheet->mergeCells('B' . $row . ':E' . $row); // Merge B, C, D, E columns for value
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);
        $row++;

        // Centro de costo
        $sheet->setCellValue('A' . $row, 'Centro de costo:');
        $sheet->setCellValue('B' . $row, $centro_costo_nombre);
        $sheet->mergeCells('B' . $row . ':E' . $row); // Merge B, C, D, E columns for value
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);
        $row++;

        // Período
        $sheet->setCellValue('A' . $row, 'Período:');
        $sheet->setCellValue('B' . $row, date('Y-m-d', strtotime($fecha_inicio)) . ' - ' . date('Y-m-d', strtotime($fecha_fin)));
        $sheet->mergeCells('B' . $row . ':E' . $row); // Merge B, C, D, E columns for value
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);

        // Apply borders to header section (columns A through E to cover merged cells)
        $headerEndRow = $row;
        $sheet->getStyle('A' . $headerStartRow . ':E' . $headerEndRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $row += 2; // One blank row for spacing (no borders, no background)

        // ENCABEZADOS DE LA TABLA
        $dataTableStartRow = $row; // Store the start row for data table borders
        $headers = ['Puesto', 'Barbero', 'Método Pago', 'Fecha Inicio', 'Fecha Fin', 'Estado', 'Total'];
        $columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];

        foreach ($headers as $index => $header) {
            $sheet->setCellValue($columns[$index] . $row, $header);
            $sheet->getStyle($columns[$index] . $row)->getFont()->setBold(true);
            $sheet->getStyle($columns[$index] . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('4472C4');
            $sheet->getStyle($columns[$index] . $row)->getFont()->getColor()->setRGB('FFFFFF');
            $sheet->getStyle($columns[$index] . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        }
        $row++;

        // DATOS DE LAS CITAS
        $total_general = 0;
        foreach ($consulta_data['citas'] as $cita) {
            $sheet->setCellValue('A' . $row, $cita['descripcion_puesto'] ?? 'N/A');
            $sheet->setCellValue('B' . $row, $cita['turno_empleado_nombre'] ?? 'N/A');
            $sheet->setCellValue('C' . $row, $cita['nombre_metodo_pago'] ?? 'N/A');

            // Formatear fechas
            $fecha_inicio_formateada = $cita['fecha_inicio'] ? date('Y-m-d H:i', strtotime($cita['fecha_inicio'])) : 'N/A';
            $fecha_fin_formateada = $cita['fecha_fin'] ? date('Y-m-d H:i', strtotime($cita['fecha_fin'])) : 'N/A';

            $sheet->setCellValue('D' . $row, $fecha_inicio_formateada);
            $sheet->setCellValue('E' . $row, $fecha_fin_formateada);

            // Estado
            $estado_texto = $cita['estado'] == 1 ? 'Finalizada' : 'Cancelada';
            if ($cita['estado'] != 1 && !empty($cita['razon_cancelacion'])) {
                $estado_texto .= ' (' . $cita['razon_cancelacion'] . ')';
            }
            $sheet->setCellValue('F' . $row, $estado_texto);

            // Total
            $total_valor = (float)$cita['total_valor_servicios'];
            $sheet->setCellValue('G' . $row, $total_valor);
            $sheet->getStyle('G' . $row)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $total_general += $total_valor;
            $row++;
        }

        // TOTAL GENERAL
        $row++; // Espacio adicional
        $sheet->setCellValue('A' . $row, 'TOTAL GENERAL:');
        $sheet->mergeCells('A' . $row . ':F' . $row);
        $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':F' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $sheet->setCellValue('G' . $row, $total_general);
        $sheet->getStyle('G' . $row)->getNumberFormat()->setFormatCode('#,##0');
        $sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G' . $row)->getFont()->setBold(true);
        $sheet->getStyle('G' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');

        // Ajustar ancho de columnas
        $sheet->getColumnDimension('A')->setWidth(20); // Puesto
        $sheet->getColumnDimension('B')->setWidth(20); // Barbero
        $sheet->getColumnDimension('C')->setWidth(18); // Método Pago
        $sheet->getColumnDimension('D')->setWidth(18); // Fecha Inicio
        $sheet->getColumnDimension('E')->setWidth(18); // Fecha Fin
        $sheet->getColumnDimension('F')->setWidth(15); // Estado
        $sheet->getColumnDimension('G')->setWidth(15); // Total

        // Agregar bordes a toda la tabla (excluding the blank row between header and data)
        $sheet->getStyle('A' . $dataTableStartRow . ':G' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // Configurar nombre del archivo
        $filename = 'Consulta_Historica_Citas_' . date('Y-m-d_H-i-s') . '.xlsx';

        // Configurar headers para descarga
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        // Crear writer y enviar archivo
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');

    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Error en generarExcelCitasHistorico: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        throw new Exception("Error al generar el archivo Excel: " . $e->getMessage());
    }
}

require_once __ROOT__ . '/views/lcitas_historico.view.php';

?>
